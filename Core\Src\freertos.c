/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "sys.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
uint8_t readBuffer[16];

/* USER CODE END Variables */
osThreadId defaultTaskHandle;
osThreadId UartSendHandle;
osThreadId UartProcHandle;
osThreadId TaskOneHandle;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void const * argument);
void vUartSend(void const * argument);
void vUartProc(void const * argument);
void vTaskOne(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of defaultTask */
  osThreadDef(defaultTask, StartDefaultTask, osPriorityNormal, 0, 128);
  defaultTaskHandle = osThreadCreate(osThread(defaultTask), NULL);

  /* definition and creation of UartSend */
  osThreadDef(UartSend, vUartSend, osPriorityNormal, 0, 128);
  UartSendHandle = osThreadCreate(osThread(UartSend), NULL);

  /* definition and creation of UartProc */
  osThreadDef(UartProc, vUartProc, osPriorityNormal, 0, 128);
  UartProcHandle = osThreadCreate(osThread(UartProc), NULL);

  /* definition and creation of TaskOne */
  osThreadDef(TaskOne, vTaskOne, osPriorityBelowNormal, 0, 128);
  TaskOneHandle = osThreadCreate(osThread(TaskOne), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void const * argument)
{
  /* USER CODE BEGIN StartDefaultTask */
	HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_3);
  HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_4);
	HAL_UARTEx_ReceiveToIdle_IT(&huart1, readBuffer, sizeof(readBuffer));
//	HAL_TIM_Base_Start(&htim4);
//	osDelay(200);
//	jy61pInit();
  /* Infinite loop */
  for(;;)
  {
    osDelay(10);
  }
  /* USER CODE END StartDefaultTask */
}

/* USER CODE BEGIN Header_vUartSend */
/**
* @brief Function implementing the UartSend thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_vUartSend */
__weak void vUartSend(void const * argument)
{
  /* USER CODE BEGIN vUartSend */
	static float tx_data_gyro[3];
	static uint8_t tail[4] = {0x00, 0x00, 0x80, 0x7f};
  /* Infinite loop */
  for(;;)
  {
    osDelay(20);
//		Gyro_Struct *JY61P_Data = get_angle();
//		tx_data_gyro[0] = JY61P_Data->x;
//		tx_data_gyro[1] = JY61P_Data->y;
//		tx_data_gyro[2] = JY61P_Data->z;
//		HAL_UART_Transmit(&huart1, (uint8_t *)tx_data_gyro, sizeof(float) * 3, 0xffff);  // 陀螺仪数据
//		HAL_UART_Transmit(&huart1, (uint8_t *)tail, sizeof(uint8_t) * 4, 0xffff);
  }
  /* USER CODE END vUartSend */
}

/* USER CODE BEGIN Header_vUartProc */
/**
* @brief Function implementing the UartProc thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_vUartProc */
__weak void vUartProc(void const * argument)
{
  /* USER CODE BEGIN vUartProc */
	static int rx_bufLength = 0;
	static uint8_t rx_buf[16];
	static float tx_data[2];
  /* Infinite loop */
  for(;;)
  {
    osDelay(20);
		rx_bufLength = Command_GetCommand(rx_buf);
		if (rx_bufLength != 0) {
			if (rx_bufLength == 0x07) {
				if (rx_buf[2] == 0x01) {  // �϶��Ŀ��Ƕ�
					tx_data[0] = hex_to_float(rx_buf, 3);
					up_servo.target_angle = tx_data[0];
				} else if (rx_buf[2] == 0x02) {  // �¶��Ŀ��Ƕ�
					tx_data[1] = hex_to_float(rx_buf, 3);
					down_servo.target_angle = tx_data[1];
				}
			}
		}		
  }
  /* USER CODE END vUartProc */
}

/* USER CODE BEGIN Header_vTaskOne */
/**
* @brief Function implementing the TaskOne thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_vTaskOne */
__weak void vTaskOne(void const * argument)
{
  /* USER CODE BEGIN vTaskOne */
  /* Infinite loop */
  for(;;)
  {
    osDelay(20);
		ToGo_Angle();
  }
  /* USER CODE END vTaskOne */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) {
  if (huart == &huart1) {
    Command_Write(readBuffer, Size);
    HAL_UARTEx_ReceiveToIdle_IT(&huart1, readBuffer, sizeof(readBuffer));
  }
}
/* USER CODE END Application */
